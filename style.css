* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.container {
    text-align: center;
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #333;
    margin-bottom: 2rem;
}

.status {
    margin-bottom: 2rem;
    font-size: 1.2rem;
    color: #666;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 2rem;
}

.cell {
    width: 100px;
    height: 100px;
    background-color: #f8f8f8;
    border: 2px solid #333;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.cell:hover {
    background-color: #eee;
}

.restart-btn {
    padding: 10px 20px;
    font-size: 1rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.restart-btn:hover {
    background-color: #45a049;
}

/* Winner styles */
.cell.winner {
    background-color: #90EE90;
    color: #006400;
}